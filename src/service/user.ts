import { object, string } from "superstruct";
import type { Infer } from "superstruct";
import { Service } from "../service.ts";
import { Path } from "../util/path.ts";
import { err, ok, type Result } from "neverthrow";

export class UserService extends Service {
	readonly path = new Path("user");

	public async login(
		username: string,
		password: string,
	): Promise<Result<LoginResponse, Error>> {
		const res = await this.client.Get(
			this.path.join("login").toString(),
			{
				username,
				password,
			},
			LoginResponse,
		);

		if (res.isErr()) {
			return err(new Error(res.error.message));
		}

		if (!res.value.httpStatusOk) {
			return err(new Error(res.value.error));
		}

		return ok(res.value.data);
	}
}

const LoginResponse = object({
	displayName: string(),
	username: string(),
	token: string(),
	status: string(),
});

export type LoginResponse = Infer<typeof LoginResponse>;
