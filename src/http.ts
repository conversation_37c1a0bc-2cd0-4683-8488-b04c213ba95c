import { err, ok, type Result, ResultAsync } from "neverthrow";
import type { Struct } from "superstruct";
import { fetch, Request, URL } from "./util/safe.ts";

export class HttpClient {
	private readonly baseUrl: string;
	private readonly protocol: Protocol;

	constructor(baseUrl: string, protocol: Protocol = Protocol.HTTPS) {
		this.baseUrl = baseUrl;
		this.protocol = protocol;
	}

	/**
	 * Performs a GET request.
	 *
	 * @param endpoint Endpoint to request. Will be appended to the base URL.
	 * @param params URL parameters to set.
	 * @param schema Schema to validate the response data against.
	 * @returns A result containing the response data if successful, or an error
	 * if not.
	 */
	public async Get<T>(
		endpoint: string,
		params?: Record<string, string>,
		schema?: Struct<T>,
	): Promise<Result<HttpClientResponse<T>, Error>> {
		const req = this.NewGetRequest(endpoint, params);
		if (req.isErr()) {
			return err(req.error);
		}

		const resp = await fetch(req.value);
		if (resp.isErr()) {
			return err(resp.error);
		}

		if (!resp.value.ok) {
			return ok({
				httpStatusOk: false,
				httpStatusCode: resp.value.status,
				error: `Request failed: ${resp.value.status}`,
			});
		}

		const data: Result<T, Error> = await ResultAsync.fromPromise<T, Error>(
			resp.value.json(),
			(err) => new Error(`Failed to parse JSON: ${err}`),
		);
		if (data.isErr()) {
			return err(data.error);
		}

		const validatedData = HttpClient.validate(data.value, schema);
		if (validatedData.isErr()) {
			return err(validatedData.error);
		}

		return ok({
			httpStatusOk: true,
			httpStatusCode: resp.value.status,
			data: validatedData.value,
		});
	}

	/**
	 * Creates a new POST request.
	 *
	 * @param endpoint Endpoint to request. Will be appended to the base URL.
	 * @param contentType Content type of the request.
	 * @param body Body of the request.
	 * @param params URL parameters to set.
	 * @returns A new request.
	 */
	private NewPostRequest(
		endpoint: string,
		contentType: ContentType,
		body: BodyInit,
		params?: Record<string, string>,
	): Result<Request, Error> {
		return this.NewRequest(
			HttpMethod.POST,
			endpoint,
			contentType,
			params,
			body,
		);
	}

	/**
	 * Creates a new GET request.
	 *
	 * @param endpoint Endpoint to request. Will be appended to the base URL.
	 * @param params URL parameters to set.
	 * @returns A new request.
	 */
	private NewGetRequest(
		endpoint: string,
		params?: Record<string, string>,
	): Result<Request, Error> {
		return this.NewRequest(HttpMethod.GET, endpoint, undefined, params);
	}

	/**
	 * Creates a new request.
	 *
	 * @param method HTTP method to use.
	 * @param endpoint Endpoint to request. Will be appended to the base URL.
	 * @param contentType Content type of the request.
	 * @param params URL parameters to set.
	 * @param body Body of the request.
	 * @returns A new request.
	 */
	private NewRequest(
		method: HttpMethod,
		endpoint: string,
		contentType?: ContentType,
		params?: Record<string, string>,
		body?: BodyInit,
	): Result<Request, Error> {
		const url = URL(
			HttpClient.sanitizeUrl(endpoint),
			`${this.protocol}://${this.baseUrl}`,
		).andThen((url) => {
			if (params) {
				return ok(HttpClient.setUrlParams(url, params));
			}
			return ok(url);
		});

		if (url.isErr()) {
			return err(url.error);
		}

		const headers = new Headers();
		if (contentType) {
			headers.set("Content-Type", contentType);
		}

		return Request(url.value.toString(), {
			method,
			headers,
			body,
		});
	}

	/**
	 * Sanitizes the given URL by removing:
	 * - Leading and trailing whitespace
	 * - Protocol
	 * - Trailing slash
	 *
	 * @param url URL to sanitize.
	 * @returns Sanitized URL.
	 */
	private static sanitizeUrl(url: string) {
		return url
			.trim() // Remove leading and trailing whitespace
			.replace(/^.+:\/\//, "") // Remove protocol
			.replace(/\/+$/, ""); // Remove trailing slash
	}

	/**
	 * Sets URL parameters on the given URL.
	 *
	 * @param url URL to set parameters on.
	 * @param params Parameters to set.
	 * @returns A copy of the given URL with the parameters set.
	 */
	private static setUrlParams(url: URL, params: Record<string, string>) {
		const res = URL(url).andThen((url) => {
			for (const [key, value] of Object.entries(params)) {
				url.searchParams.set(key, value);
			}
			return ok(url);
		});
		if (res.isErr()) {
			return err(res.error);
		}

		return res.value;
	}

	/**
	 * Validates the given data against the given schema. If no schema is
	 * provided, the data is returned as-is.
	 *
	 * @param data Data to validate.
	 * @param schema Schema to validate against.
	 * @returns The validated data if successful, or an error if not.
	 */
	private static validate<T>(
		data: unknown,
		schema?: Struct<T>,
	): Result<T, Error> {
		if (!schema) {
			return ok(data as T);
		}

		const [error, res] = schema.validate(data);
		if (error) {
			return err(new Error(`Validation error: ${error.message}`));
		}

		return ok(res);
	}
}

/**
 * Represents the HTTP protocol to use for a request.
 */
export enum Protocol {
	HTTP = "http",
	HTTPS = "https",
}

/**
 * Represents the HTTP method to use for a request.
 */
export enum HttpMethod {
	GET = "GET",
	POST = "POST",
}

/**
 * Represents the content type of a POST request.
 */
export enum ContentType {
	FORM_URL_ENCODED = "application/x-www-form-urlencoded",
	MULTIPART_FORM_DATA = "multipart/form-data",
	JSON = "application/json",
	TEXT = "text/plain",
}

interface HttpClientErrorResponse {
	readonly httpStatusOk: false;
	readonly httpStatusCode: number;
	readonly error: string;
}

interface HttpClientSuccessResponse<T> {
	readonly httpStatusOk: true;
	readonly httpStatusCode: number;
	readonly data: T;
}

export type HttpClientResponse<T> =
	| HttpClientErrorResponse
	| HttpClientSuccessResponse<T>;
