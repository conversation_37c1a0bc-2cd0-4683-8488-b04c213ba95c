import { Result, ResultAsync } from "neverthrow";

/**
 * Safe wrapper for native fetch.
 *
 * @see https://developer.mozilla.org/en-US/docs/Web/API/fetch
 */
export const fetch = ResultAsync.fromThrowable(
	globalThis.fetch,
	(error) => new Error(`Failed to fetch: ${error}`),
);

/**
 * Safe wrapper for native Request.
 *
 * @see https://developer.mozilla.org/en-US/docs/Web/API/Request/Request
 */
export const Request = Result.fromThrowable(
	(...args: ConstructorParameters<typeof globalThis.Request>) =>
		new globalThis.Request(...args),
	(error) => new Error(`Failed to create request: ${error}`),
);

/**
 * Safe wrapper for native JSON.
 *
 * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON
 */
export const SafeJSON = {
	parse: Result.fromThrowable(
		globalThis.JSON.parse,
		(error) => new Error(`Failed to parse JSON: ${error}`),
	),
	stringify: Result.fromThrowable(
		globalThis.JSON.stringify,
		(error) => new Error(`Failed to stringify JSON: ${error}`),
	),
};

/**
 * Safe wrapper for native URL.
 *
 * @see https://developer.mozilla.org/en-US/docs/Web/API/URL/URL
 */
export const URL = Result.fromThrowable(
	(...args: ConstructorParameters<typeof globalThis.URL>) =>
		new globalThis.URL(...args),
	(error) => new Error(`Failed to create URL: ${error}`),
);
