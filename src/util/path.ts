export class Path {
	private readonly _path: string;

	constructor(path: string) {
		this._path = Path.sanitize(path);
	}

	public join(...paths: string[]): Path {
		return new Path(
			[
				this._path,
				...paths.map((p) => Path.sanitize(p)).filter((p) => p.length > 0),
			].join("/"),
		);
	}

	public toString() {
		return this._path;
	}

	private static sanitize(path: string) {
		return encodeURIComponent(
			path
				.trim() // remove leading and trailing whitespace
				.replace(/\/+/g, "/") // remove duplicate slashes
				.replace(/\/$/, "") // remove trailing slash
				.replace(/^\//, ""), // remove leading slash
		);
	}
}
